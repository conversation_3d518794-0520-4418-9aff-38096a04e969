<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>Hierarchical Collapse Test</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        .container { width: 95%; margin: 0 auto; }
        .chart { border: 1px solid #bbb; border-radius: 4px; margin-top: 32px; height: 600px; }
        .controls { margin: 20px 0; }
        .controls button { margin-right: 10px; padding: 8px 16px; }
        .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px; }
        .instructions { margin: 10px 0; padding: 10px; background: #e3f2fd; border-radius: 4px; }
        
        /* 自定义任务颜色 */
        .bar-project .bar { fill: #4f8cff !important; }
        .bar-task .bar { fill: #34c759 !important; }
        .bar-subtask .bar { fill: #ff9500 !important; }
        .bar-subsubtask .bar { fill: #ff3b30 !important; }
        
        /* 进度条颜色 */
        .bar-project .bar-progress { fill: #1e40af !important; }
        .bar-task .bar-progress { fill: #15803d !important; }
        .bar-subtask .bar-progress { fill: #b45309 !important; }
        .bar-subsubtask .bar-progress { fill: #dc2626 !important; }
    </style>
    <script src="../dist/frappe-gantt.umd.js"></script>
</head>
<body>
<div class="container">
    <h1>Hierarchical Collapse Test</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Click the collapse icon next to "Project #1" - should hide ALL descendants (Task1, Task2, Task2.1, Task2.1.1, Task2.1.2, Task2.2)</li>
            <li>Click the collapse icon next to "Task #2" - should hide Task2.1, Task2.1.1, Task2.1.2, Task2.2</li>
            <li>Click the collapse icon next to "Task #2.1" - should hide Task2.1.1, Task2.1.2</li>
        </ol>
    </div>
    
    <div class="status" id="status">
        Status: Ready for testing
    </div>
    
    <div class="controls">
        <button id="expand-all-btn">展开所有</button>
        <button id="collapse-all-btn">折叠所有</button>
        <button id="log-structure-btn">记录层级结构</button>
    </div>
    
    <div class="chart" id="hierarchy-test"></div>
</div>

<script>
    let gantt;
    
    function updateStatus(message) {
        document.getElementById('status').textContent = 'Status: ' + message;
        console.log('Status:', message);
    }
    
    function logTreeStructure() {
        console.log('=== Tree Structure ===');
        gantt.tasks.forEach(task => {
            const indent = '  '.repeat(task._level || 0);
            console.log(`${indent}${task.name} (${task.id}) - Level: ${task._level}, Parent: ${task._parent}, Children: [${task._children?.join(', ') || ''}], Visible: ${task._visible}, Expanded: ${task._expanded}`);
        });
        console.log('======================');
    }
    
    // 测试数据 - 4层层级结构
    const testData = [
        // Level 0: Project
        {
            id: 'Project1',
            name: 'Project #1',
            start: '2023-04-02',
            end: '2023-05-04',
            progress: 45,
            custom_class: 'bar-project',
            priority: 'Medium'
        },
        
        // Level 1: Main Tasks
        {
            id: 'Task1',
            name: 'Task #1',
            start: '2023-04-03',
            end: '2023-04-08',
            progress: 80,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task2',
            name: 'Task #2',
            start: '2023-04-03',
            end: '2023-04-20',
            progress: 30,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        
        // Level 2: Sub Tasks
        {
            id: 'Task2_1',
            name: 'Task #2.1',
            start: '2023-04-03',
            end: '2023-04-10',
            progress: 60,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Medium'
        },
        {
            id: 'Task2_2',
            name: 'Task #2.2',
            start: '2023-04-15',
            end: '2023-04-20',
            progress: 40,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        
        // Level 3: Sub-Sub Tasks
        {
            id: 'Task2_1_1',
            name: 'Task #2.1.1',
            start: '2023-04-03',
            end: '2023-04-06',
            progress: 100,
            dependencies: 'Task2_1',
            custom_class: 'bar-subsubtask',
            priority: 'High'
        },
        {
            id: 'Task2_1_2',
            name: 'Task #2.1.2',
            start: '2023-04-07',
            end: '2023-04-10',
            progress: 20,
            dependencies: 'Task2_1',
            custom_class: 'bar-subsubtask',
            priority: 'Medium'
        }
    ];
    
    try {
        updateStatus('Initializing Gantt chart with 4-level hierarchy...');
        
        // 初始化甘特图
        gantt = new Gantt('#hierarchy-test', testData, {
            view_mode: 'Day',
            bar_height: 32,
            bar_corner_radius: 8,
            arrow_curve: 8,
            padding: 20,
            // 启用表格树功能
            table_tree: {
                enabled: true,
                show_table: true,
                table_width: 350,
                indent_width: 20,
                expand_icon: '▶',
                collapse_icon: '▼',
                columns: [
                    { key: 'name', title: 'Task name', width: 200 },
                    { key: 'start', title: 'Start time', width: 80 },
                    { key: 'duration', title: 'Duration', width: 70 }
                ]
            },
            // 事件回调
            on_task_toggle: function(task, expanded) {
                updateStatus(`Task ${task.name} ${expanded ? 'expanded' : 'collapsed'}`);
                console.log(`Task ${task.name} ${expanded ? 'expanded' : 'collapsed'}`);
                logTreeStructure();
            }
        });
        
        updateStatus('Gantt chart initialized. Test hierarchical collapse!');
        logTreeStructure();
        
    } catch (error) {
        updateStatus('Error: ' + error.message);
        console.error('Initialization error:', error);
    }
    
    // 绑定按钮事件
    document.getElementById('expand-all-btn').onclick = () => {
        try {
            updateStatus('Expanding all tasks...');
            gantt.expand_all();
            updateStatus('All tasks expanded');
            logTreeStructure();
        } catch (error) {
            updateStatus('Expand failed: ' + error.message);
            console.error('Expand error:', error);
        }
    };
    
    document.getElementById('collapse-all-btn').onclick = () => {
        try {
            updateStatus('Collapsing all tasks...');
            gantt.collapse_all();
            updateStatus('All tasks collapsed');
            logTreeStructure();
        } catch (error) {
            updateStatus('Collapse failed: ' + error.message);
            console.error('Collapse error:', error);
        }
    };
    
    document.getElementById('log-structure-btn').onclick = () => {
        updateStatus('Logging tree structure to console...');
        logTreeStructure();
        updateStatus('Tree structure logged to console');
    };
</script>
</body>
</html>
