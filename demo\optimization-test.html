<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>Table Tree Optimization Test</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        .container { width: 95%; margin: 0 auto; }
        .chart { border: 1px solid #bbb; border-radius: 4px; margin-top: 32px; height: 600px; overflow: hidden; }
        .controls { margin: 20px 0; }
        .controls button { margin-right: 10px; padding: 8px 16px; }
        .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px; }
        .instructions { margin: 10px 0; padding: 10px; background: #e3f2fd; border-radius: 4px; }
        
        /* 自定义任务颜色 */
        .bar-project .bar { fill: #4f8cff !important; }
        .bar-task .bar { fill: #34c759 !important; }
        .bar-subtask .bar { fill: #ff9500 !important; }
        .bar-subsubtask .bar { fill: #ff3b30 !important; }
        
        /* 进度条颜色 */
        .bar-project .bar-progress { fill: #1e40af !important; }
        .bar-task .bar-progress { fill: #15803d !important; }
        .bar-subtask .bar-progress { fill: #b45309 !important; }
        .bar-subsubtask .bar-progress { fill: #dc2626 !important; }
    </style>
    <script src="../dist/frappe-gantt.umd.js"></script>
</head>
<body>
<div class="container">
    <h1>Table Tree Optimization Test</h1>
    
    <div class="instructions">
        <h3>Optimizations to Test:</h3>
        <ol>
            <li><strong>Hierarchical Collapse:</strong> Collapsing a parent should hide ALL descendants (grandchildren, etc.)</li>
            <li><strong>Width Constraint:</strong> Gantt chart should not overflow the container width</li>
            <li><strong>Reduced Empty Dates:</strong> Less empty space on both sides, automatic scroll to task area</li>
        </ol>
    </div>
    
    <div class="status" id="status">
        Status: Ready for testing
    </div>
    
    <div class="controls">
        <button id="expand-all-btn">展开所有</button>
        <button id="collapse-all-btn">折叠所有</button>
        <button id="toggle-table-btn">切换表格显示</button>
        <button id="scroll-start-btn">滚动到开始</button>
        <button id="scroll-today-btn">滚动到今天</button>
    </div>
    
    <div class="chart" id="optimization-test"></div>
</div>

<script>
    let gantt;
    
    function updateStatus(message) {
        document.getElementById('status').textContent = 'Status: ' + message;
        console.log('Status:', message);
    }
    
    // 测试数据 - 4层层级结构，时间跨度较长
    const testData = [
        // Level 0: Project
        {
            id: 'Project1',
            name: 'Project #1',
            start: '2023-04-02',
            end: '2023-06-30',
            progress: 45,
            custom_class: 'bar-project',
            priority: 'Medium'
        },
        
        // Level 1: Main Tasks
        {
            id: 'Task1',
            name: 'Task #1',
            start: '2023-04-03',
            end: '2023-04-15',
            progress: 80,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task2',
            name: 'Task #2',
            start: '2023-04-10',
            end: '2023-05-20',
            progress: 30,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task3',
            name: 'Task #3',
            start: '2023-05-15',
            end: '2023-06-30',
            progress: 10,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'Medium'
        },
        
        // Level 2: Sub Tasks
        {
            id: 'Task2_1',
            name: 'Task #2.1',
            start: '2023-04-10',
            end: '2023-04-25',
            progress: 60,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Medium'
        },
        {
            id: 'Task2_2',
            name: 'Task #2.2',
            start: '2023-05-01',
            end: '2023-05-20',
            progress: 40,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        {
            id: 'Task3_1',
            name: 'Task #3.1',
            start: '2023-05-15',
            end: '2023-06-10',
            progress: 20,
            dependencies: 'Task3',
            custom_class: 'bar-subtask',
            priority: 'High'
        },
        
        // Level 3: Sub-Sub Tasks
        {
            id: 'Task2_1_1',
            name: 'Task #2.1.1',
            start: '2023-04-10',
            end: '2023-04-17',
            progress: 100,
            dependencies: 'Task2_1',
            custom_class: 'bar-subsubtask',
            priority: 'High'
        },
        {
            id: 'Task2_1_2',
            name: 'Task #2.1.2',
            start: '2023-04-18',
            end: '2023-04-25',
            progress: 20,
            dependencies: 'Task2_1',
            custom_class: 'bar-subsubtask',
            priority: 'Medium'
        },
        {
            id: 'Task3_1_1',
            name: 'Task #3.1.1',
            start: '2023-05-15',
            end: '2023-05-30',
            progress: 50,
            dependencies: 'Task3_1',
            custom_class: 'bar-subsubtask',
            priority: 'Medium'
        },
        {
            id: 'Task3_1_2',
            name: 'Task #3.1.2',
            start: '2023-06-01',
            end: '2023-06-10',
            progress: 0,
            dependencies: 'Task3_1',
            custom_class: 'bar-subsubtask',
            priority: 'Low'
        }
    ];
    
    try {
        updateStatus('Initializing optimized Gantt chart...');
        
        // 初始化甘特图
        gantt = new Gantt('#optimization-test', testData, {
            view_mode: 'Day',
            bar_height: 32,
            bar_corner_radius: 8,
            arrow_curve: 8,
            padding: 20,
            // 启用表格树功能
            table_tree: {
                enabled: true,
                show_table: true,
                table_width: 350,
                indent_width: 20,
                expand_icon: '▶',
                collapse_icon: '▼',
                columns: [
                    { key: 'name', title: 'Task name', width: 200 },
                    { key: 'start', title: 'Start time', width: 80 },
                    { key: 'duration', title: 'Duration', width: 70 }
                ]
            },
            // 事件回调
            on_task_toggle: function(task, expanded) {
                updateStatus(`Task ${task.name} ${expanded ? 'expanded' : 'collapsed'}`);
            }
        });
        
        updateStatus('Gantt chart initialized with optimizations!');
        
    } catch (error) {
        updateStatus('Error: ' + error.message);
        console.error('Initialization error:', error);
    }
    
    // 绑定按钮事件
    document.getElementById('expand-all-btn').onclick = () => {
        try {
            updateStatus('Expanding all tasks...');
            gantt.expand_all();
            updateStatus('All tasks expanded');
        } catch (error) {
            updateStatus('Expand failed: ' + error.message);
        }
    };
    
    document.getElementById('collapse-all-btn').onclick = () => {
        try {
            updateStatus('Collapsing all tasks...');
            gantt.collapse_all();
            updateStatus('All tasks collapsed');
        } catch (error) {
            updateStatus('Collapse failed: ' + error.message);
        }
    };
    
    document.getElementById('toggle-table-btn').onclick = () => {
        try {
            updateStatus('Toggling table display...');
            const currentShow = gantt.options.table_tree.show_table;
            gantt.options.table_tree.show_table = !currentShow;
            gantt.render();
            updateStatus(`Table ${!currentShow ? 'shown' : 'hidden'}`);
        } catch (error) {
            updateStatus('Toggle failed: ' + error.message);
        }
    };
    
    document.getElementById('scroll-start-btn').onclick = () => {
        try {
            updateStatus('Scrolling to start...');
            gantt.set_scroll_position('start');
            updateStatus('Scrolled to start');
        } catch (error) {
            updateStatus('Scroll failed: ' + error.message);
        }
    };
    
    document.getElementById('scroll-today-btn').onclick = () => {
        try {
            updateStatus('Scrolling to today...');
            gantt.set_scroll_position('today');
            updateStatus('Scrolled to today');
        } catch (error) {
            updateStatus('Scroll failed: ' + error.message);
        }
    };
</script>
</body>
</html>
